/** @module lib::log */

module.exports = {
  /**
   * Produces an info log.
   *
   * @param { any } data The data to be logged.
   * @return { void }
   */
  info: (message, data) => {
    console.info(
      JSON.stringify({
        severity: "INFO",
        message,
        data,
      }),
    );
  },

  /**
   * Produces an error log.
   *
   * @param { Error } error The error object to be logged.
   * @return { void }
   */
  error: (error) => {
    console.error(
      JSON.stringify({
        severity: "ERROR",
        message: error.message,
        data: error.data || null,
        http_response: error.response ? JSON.parse(JSON.stringify(error.response.data)) : null,
        stack: error.stack
          ? error.stack
              .split("\n")
              .slice(1)
              .map((item) => item.trim())
          : null,
      }),
    );
  },
};
