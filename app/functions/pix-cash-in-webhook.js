/** @module functions::pixCashInWebhook */

const { PubSub } = require("@google-cloud/pubsub");

const log = require("../lib/log");

const pubSub = new PubSub();

/**
 * Converts the Pix Cash In notification from the external idiom to the our specification.
 *
 * @private
 * @param { Object } notification A notification to be converted.
 * @returns { Object } The converted notification.
 */
function convert(notification) {
  const {
    pix: { endToEndId, transactionID },
  } = notification;

  return {
    id: transactionID,
    bank_receipt_url: null,
    end2end_id: endToEndId,
  };
}

/**
 * Responds to requests sent by <PERSON>oo<PERSON> containing notifications about Pix Cash In status updates.
 *
 * @see {@link https://developers.woovi.com/docs/webhook}
 * @memberof functions::pixCashInWebhook
 * @param req { Object } {@link https://expressjs.com/en/api.html#req}
 * @param res { Object } {@link https://expressjs.com/en/api.html#res}
 * @returns { Promise<void> }
 */
module.exports = async (req, res) => {
  try {
    log.info("pixCashInWebhook", req.body);

    const { event, pix } = req.body;
    const transactionID = pix?.transactionID;

    if (event === "OPENPIX:TRANSACTION_RECEIVED" && transactionID) {
      await pubSub.topic("payments-on-pix-cashin").publishMessage({
        data: Buffer.from(JSON.stringify(convert(req.body))),
      });
    }

    res.status(200).send();
  } catch (error) {
    log.error(error);

    res.status(500).send();
  }
};
