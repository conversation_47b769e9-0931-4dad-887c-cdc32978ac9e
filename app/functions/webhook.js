/** @module functions::webhook */

const { PubSub } = require("@google-cloud/pubsub");

const log = require("../lib/log");
const pubSub = new PubSub();

/**
 * Converts the payment notification from the external idiom to the our specification.
 *
 * @private
 * @param { Object } notification A notification to be converted.
 * @returns { Object } The converted notification.
 */
function convertFromPayment(notification) {
  return {
    entry_id: notification?.payment?.correlationID,
    bank_receipt_url: null,
    status: { confirmed: "success", failed: "fail" }[notification?.payment?.status.toLowerCase()],
  };
}

/**
 * Converts the account check notification from the external idiom to the our specification.
 *
 * @private
 * @param { Object } notification
 * @returns { Object } The converted notification.
 */
function convertFromAccountCheck(notification) {
  const [tasker_id, tasker_bank_id, bank_hash] = notification.payment.correlationID.split(":");
  const status = { confirmed: "success", failed: "fail" }[notification.payment.status.toLowerCase()];

  return {
    tasker_id,
    tasker_bank_id,
    bank_hash,
    status,
    errors: status === "fail" ? ["Erro desconhecido"] : [], // Woovi does not provide errors, yet.
  };
}

/**
 * Identifies and converts the notification to the correct type of webhook.
 *
 * @private
 * @param { Object } notification A notification to be converted.
 * @returns { [string, Object] } The converted notification.
 */
function convertToTopicAndMessage(notification) {
  let topic;
  let message;

  if (!notification?.payment?.correlationID.includes(":")) {
    topic = "payments-on-status-update";
    message = convertFromPayment(notification);
  } else if (notification?.payment?.correlationID.includes(":")) {
    topic = "payments-on-account-check";
    message = convertFromAccountCheck(notification);
  }

  return [topic, message];
}

/**
 * Responds to requests sent by Woovi containing notifications about payment status updates.
 *
 * @see {@link https://docs.transfeera.dev/pagamentos/webhook}
 * @memberof functions::webhook
 * @param req { Object } {@link https://expressjs.com/en/api.html#req}
 * @param res { Object } {@link https://expressjs.com/en/api.html#res}
 * @returns { Promise<void> }
 */
module.exports = async (req, res) => {
  try {
    log.info("webhook", req.body);

    const [topic, message] = convertToTopicAndMessage(req.body);
    await pubSub.topic(topic).publishMessage({
      data: Buffer.from(JSON.stringify(message)),
    });

    res.status(200).send();
  } catch (error) {
    log.error(error);

    res.status(500).send();
  }
};
