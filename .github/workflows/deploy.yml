name: deploy

on:
  workflow_dispatch:
  release:
    types: [published]

env:
  PROJECT: ${{ github.event_name == 'release' && github.event.action == 'published' && 'parafuzo-infra' || 'parafuzo-qa-infra' }}

jobs:
  deploy-process-payment-batch:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-process-payment-batch \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point processPaymentBatch \
            --verbosity error \
            --trigger-topic payments-do-pay \
            --max-instances 1

  deploy-pay:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-pay \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point pay \
            --verbosity error \
            --set-env-vars "WOOVI_API_ENDPOINT=${{ vars.WOOVI_API_ENDPOINT }}" \
            --set-secrets "WOOVI_API_KEY=woovi-api-key:latest" \
            --trigger-topic woovi-do-pay \
            --max-instances 1

  deploy-check-account:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-check-account \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point checkAccount \
            --verbosity error \
            --set-env-vars "WOOVI_API_ENDPOINT=${{ vars.WOOVI_API_ENDPOINT }}" \
            --set-secrets "WOOVI_API_KEY=woovi-api-key:latest" \
            --trigger-topic payments-do-check-account \
            --max-instances 1

  deploy-webhook:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-webhook \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point webhook \
            --verbosity error \
            --trigger-http \
            --allow-unauthenticated \
            --max-instances 5

  deploy-pix-cash-in-webhook:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-pix-cash-in-webhook \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point pixCashInWebhook \
            --verbosity error \
            --trigger-http \
            --allow-unauthenticated \
            --max-instances 5
