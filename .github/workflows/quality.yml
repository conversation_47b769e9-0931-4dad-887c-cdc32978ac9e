name: quality

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - master

jobs:
  lint-and-tests:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - uses: actions/cache@v4
        id: yarn-cache
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install dependencies
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        run: yarn --frozen-lockfile

      - run: yarn lint

      - name: Test and coverage
        run: yarn test --coverage

      - name: SonarCloud Scan
        uses: SonarSource/sonarqube-scan-action@v5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          projectBaseDir: .
          args: >
            -Dsonar.organization=parafuzo
            -Dsonar.projectKey=parafuzo_service-woovi
            -Dsonar.sources=app/
            -Dsonar.exclusions=spec/**/*
            -Dsonar.tests=app/
            -Dsonar.test.inclusions=spec/**/*
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info

  auto-merge:
    uses: parafuzo/actions/.github/workflows/auto-merge.yml@master
    needs: [lint-and-tests]
