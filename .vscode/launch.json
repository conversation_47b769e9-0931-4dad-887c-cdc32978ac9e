{"version": "0.2.0", "configurations": [{"name": "Debug Jest Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache", "--no-coverage"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true, "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}, {"name": "Debug Current Jest Test", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["${fileBasenameNoExtension}", "--runInBand", "--no-cache", "--no-coverage"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true, "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}, {"name": "Debug Specific Jest Test", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["spec/functions/pay.spec.js", "--runInBand", "--no-cache", "--no-coverage"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true, "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}]}