# Confirmed webhook
POST https://us-east1-parafuzo-qa-infra.cloudfunctions.net/woovi-pix-cash-in-webhook
Content-Type: application/json

{
  "event": "OPENPIX:TRANSACTION_RECEIVED",
  "charge": null,
  "pixQrCode": {
    "name": "<PERSON>",
    "value": 111,
    "comment": "CPF: 54998550055",
    "identifier": "ecaa6f8ad5424a048317873c1",
    "correlationID": "6c5bbac0ae18539c07b252e970d2e14c",
    "paymentLinkID": "0b1df330-8e77-432f-9ac6-e676c11de3f6",
    "createdAt": "2025-07-11T02:04:32.325Z",
    "updatedAt": "2025-07-11T02:04:32.325Z",
    "brCode": "00020126780014br.gov.bcb.pix01360b17737e-f7ac-4916-bc8a-5e81ebdbcd170216CPF:_5499855005552040000530398654041.115802BR5908Parafuzo6009Sao_Paulo62290525ecaa6f8ad5424a048317873c163041C3C",
    "paymentLinkUrl": "https://woovi-sandbox.com/pay/0b1df330-8e77-432f-9ac6-e676c11de3f6",
    "qrCodeImage": "https://api.woovi-sandbox.com/openpix/charge/brcode/image/0b1df330-8e77-432f-9ac6-e676c11de3f6.png",
    "pixKey": "0b17737e-f7ac-4916-bc8a-5e81ebdbcd17"
  },
  "pix": {
    "payer": {
      "name": "Cliente Teste",
      "taxID": {
        "taxID": "44720743000101",
        "type": "BR:CNPJ"
      },
      "correlationID": "74d8423c-4d35-43e7-8867-1acc84005406"
    },
    "value": 111,
    "time": "2025-07-11T11:17:25.082Z",
    "endToEndId": "E9f93a1eab7724878b4c8c326c7a9f342",
    "transactionID": "ecaa6f8ad5424a048317873c1",
    "infoPagador": "OpenPix PixQrCode testing",
    "type": "PAYMENT",
    "pixQrCode": {
      "name": "John Doe",
      "value": 111,
      "comment": "CPF: 54998550055",
      "identifier": "ecaa6f8ad5424a048317873c1",
      "correlationID": "202507102304",
      "paymentLinkID": "0b1df330-8e77-432f-9ac6-e676c11de3f6",
      "createdAt": "2025-07-11T02:04:32.325Z",
      "updatedAt": "2025-07-11T02:04:32.325Z",
      "brCode": "00020126780014br.gov.bcb.pix01360b17737e-f7ac-4916-bc8a-5e81ebdbcd170216CPF:_5499855005552040000530398654041.115802BR5908Parafuzo6009Sao_Paulo62290525ecaa6f8ad5424a048317873c163041C3C",
      "paymentLinkUrl": "https://woovi-sandbox.com/pay/0b1df330-8e77-432f-9ac6-e676c11de3f6",
      "qrCodeImage": "https://api.woovi-sandbox.com/openpix/charge/brcode/image/0b1df330-8e77-432f-9ac6-e676c11de3f6.png",
      "pixKey": "0b17737e-f7ac-4916-bc8a-5e81ebdbcd17"
    },
    "createdAt": "2025-07-11T11:17:25.087Z",
    "globalID": "UGl4VHJhbnNhY3Rpb246Njg3MGYyYzUxY2NkZDA1Y2M4NzEyNmY5"
  },
  "company": {
    "id": "6867c763f5067b7562de19fa",
    "name": "Parafuzo",
    "taxID": "**************"
  },
  "account": {
    "accountId": "6867c763f5067b7562de1a14",
    "branch": "0001",
    "account": "3047"
  },
  "refunds": []
}