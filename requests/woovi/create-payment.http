# Create Payment
POST {{woovi_api_url}}/api/v1/payment
Authorization: {{woovi_api_key}}
Content-Type: application/json

{
  "value": 1,
  "correlationID": "64356b5593c8fc000980e4d7:686fd5f9421aa90a543e035f:894d1fed669d27fa2e6b50662331be84",
  "holder": {
    "name": "<PERSON><PERSON><PERSON>",
    "taxID": {
      "type": "BR:CPF",
      "taxID": "***********"
    }
  },
  "psp": {
    "id": "********",
    "name": "BCO DO BRASIL S.A."
  },
  "account": {
    "account": "********000000119580",
    "branch": "4060",
    "accountType": "CACC"
  }
}

###

# Approve Payment
POST {{woovi_api_url}}/api/v1/payment/approve
Authorization: {{woovi_api_key}}
Content-Type: application/json

{
  "correlationID": "64356b5593c8fc000980e4d7:686fd5f9421aa90a543e035f:894d1fed669d27fa2e6b50662331be84"
}
