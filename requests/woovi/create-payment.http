# Create Payment
POST {{woovi_api_url}}/api/v1/payment
Authorization: {{woovi_api_key}}
Content-Type: application/json

{
  "value": 24640,
  "correlationID": "687122ea421aa900cc3504ff",
  "holder": {
    "name": "<PERSON><PERSON>",
    "taxID": {
      "type": "BR:CPF",
      "taxID": "***********"
    }
  },
  "psp": {
    "id": "********",
    "name": "BCO DO BRASIL S.A."
  },
  "account": {
    "account": "********000000452360",
    "branch": "1830",
    "accountType": "CACC"
  }
}

###

# Approve Payment
POST {{woovi_api_url}}/api/v1/payment/approve
Authorization: {{woovi_api_key}}
Content-Type: application/json

{
  "correlationID": "687122ea421aa900cc3504ff"
}
