const subject = require("../app");

describe("when the file is required", () => {
  it("exports checkAccount", () => {
    expect(subject).toMatchObject({ checkAccount: expect.any(Function) });
  });

  it("exports pay", () => {
    expect(subject).toMatchObject({ pay: expect.any(Function) });
  });

  it("exports pixCashInWebhook", () => {
    expect(subject).toMatchObject({ pixCashInWebhook: expect.any(Function) });
  });

  it("exports processPaymentBatch", () => {
    expect(subject).toMatchObject({ processPaymentBatch: expect.any(Function) });
  });

  it("exports webhook", () => {
    expect(subject).toMatchObject({ webhook: expect.any(Function) });
  });
});
