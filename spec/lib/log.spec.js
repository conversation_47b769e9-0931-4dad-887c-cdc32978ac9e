const errorData = { key: "value" };
const errorResponse = { data: { key: "value" } };
const errorStack = "error message\n  at error 1\n  at error 2";

const error = new Error("error message");
error.data = errorData;
error.response = errorResponse;
error.stack = errorStack;

console.info = jest.fn();
console.error = jest.fn();

const subject = require("../../app/lib/log");

describe("#info", () => {
  describe("when receives a data to be logged", () => {
    it("logs the message", () => {
      subject.info("the log message", { the: "data" });

      expect(console.info).toHaveBeenCalledWith(
        JSON.stringify({
          severity: "INFO",
          message: "the log message",
          data: { the: "data" },
        }),
      );
    });
  });
});

describe("#error", () => {
  describe("when receives an error object", () => {
    it("logs the message", () => {
      subject.error(error);

      expect(console.error).toHaveBeenCalledWith(
        JSON.stringify({
          severity: "ERROR",
          message: error.message,
          data: error.data,
          http_response: error.response.data,
          stack: ["at error 1", "at error 2"],
        }),
      );
    });
  });

  describe("when data does not exists", () => {
    it("defaults to null", () => {
      const errorWithoutData = new Error("error message");
      errorWithoutData.response = errorResponse;
      errorWithoutData.stack = errorStack;

      subject.error(errorWithoutData);

      expect(console.error).toHaveBeenCalledWith(
        JSON.stringify({
          severity: "ERROR",
          message: errorWithoutData.message,
          data: null,
          http_response: errorWithoutData.response.data,
          stack: ["at error 1", "at error 2"],
        }),
      );
    });
  });

  describe("when response does not exists", () => {
    it("defaults to null", () => {
      const errorWithoutResponse = new Error("error message");
      errorWithoutResponse.data = errorData;
      errorWithoutResponse.stack = errorStack;

      subject.error(errorWithoutResponse);

      expect(console.error).toHaveBeenCalledWith(
        JSON.stringify({
          severity: "ERROR",
          message: errorWithoutResponse.message,
          data: errorWithoutResponse.data,
          http_response: null,
          stack: ["at error 1", "at error 2"],
        }),
      );
    });
  });

  describe("when stack does not exists", () => {
    it("defaults to null", () => {
      const errorWithoutStack = new Error("error message");
      errorWithoutStack.data = errorData;
      errorWithoutStack.response = errorResponse;
      errorWithoutStack.stack = undefined;

      subject.error(errorWithoutStack);

      expect(console.error).toHaveBeenCalledWith(
        JSON.stringify({
          severity: "ERROR",
          message: errorWithoutStack.message,
          data: errorWithoutStack.data,
          http_response: errorWithoutStack.response.data,
          stack: null,
        }),
      );
    });
  });
});
