const subject = require("../../app/lib/woovi");

describe("#bankAccount", () => {
  it("applies rule with 'X' for Banco do Brasil", () => {
    const result = subject.bankAccount("001", "5060X");

    expect(result).toBe("50600");
  });

  it("does not apply rule without 'X' for Banco do Brasil", () => {
    const result = subject.bankAccount("001", "50601");

    expect(result).toBe("50601");
  });

  it("does not apply rule for Bradesco", () => {
    const result = subject.bankAccount("237", "50601");

    expect(result).toBe("50601");
  });

  it("does not apply rule for Nubank", () => {
    const result = subject.bankAccount("260", "50601");

    expect(result).toBe("50601");
  });
});

describe("#accountType", () => {
  describe("when the type is checking", () => {
    it('converts to "CACC"', () => {
      const type = subject.accountType("checking");

      expect(type).toBe("CACC");
    });
  });

  describe("when the type is savings", () => {
    it('converts to "SVGS"', () => {
      const type = subject.accountType("savings");

      expect(type).toBe("SVGS");
    });
  });

  describe("when the type is unknown", () => {
    it("converts to undefined", () => {
      const type = subject.accountType("unknown");

      expect(type).toBe(undefined);
    });
  });
});

describe("#pspBank", () => {
  it("converts from unknown to undefined", () => {
    const result = subject.pspBank("unknown");

    expect(result).toBe(undefined);
  });

  it("converts from 237 - BRADESCO", () => {
    const result = subject.pspBank("237");

    expect(result).toEqual({ id: "********", name: "BCO BRADESCO S.A." });
  });

  it("converts from 001 - BANCO DO BRASIL", () => {
    const result = subject.pspBank("001");

    expect(result).toEqual({ id: "********", name: "BCO DO BRASIL S.A." });
  });

  it("converts from 033 - SANTANDER", () => {
    const result = subject.pspBank("033");

    expect(result).toEqual({ id: "********", name: "BCO SANTANDER (BRASIL) S.A." });
  });

  it("converts from 104 - CAIXA ECONÔNIMICA", () => {
    const result = subject.pspBank("104");

    expect(result).toEqual({ id: "********", name: "CAIXA ECONOMICA FEDERAL" });
  });

  it("converts from 341 - ITAÚ", () => {
    const result = subject.pspBank("341");

    expect(result).toEqual({ id: "********", name: "ITAU UNIBANCO S.A." });
  });
});
