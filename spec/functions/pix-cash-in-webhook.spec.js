const log = require("../../app/lib/log");

log.info = jest.fn().mockResolvedValue();
log.error = jest.fn().mockResolvedValue();

const mockPublish = jest.fn().mockResolvedValue();
const mockTopic = jest.fn().mockReturnValue({
  publishMessage: mockPublish,
});

jest.mock("@google-cloud/pubsub", () => ({
  PubSub: jest.fn().mockImplementation(() => ({
    topic: mockTopic,
  })),
}));

const mockRequest = (body) => ({
  body,
});

const mockResponse = () => {
  const response = {};

  response.status = jest.fn().mockReturnValue(response);
  response.send = jest.fn().mockReturnValue(response);

  return response;
};

const subject = require("../../app/functions/pix-cash-in-webhook");

beforeEach(() => {
  log.info.mockClear();
  log.error.mockClear();

  mockPublish.mockClear();
  mockTopic.mockClear();
});

const payload = {
  event: "OPENPIX:TRANSACTION_RECEIVED",
  pixQrCode: {
    correlationID: "6870fa0e12dfd51ebc24f842",
  },
  pix: {
    endToEndId: "E9f93a1eab7724878b4c8c326c7a9f342",
  },
};

describe("#pixCashInWebhook", () => {
  it("responds with status 200", async () => {
    const req = mockRequest(payload);
    const res = mockResponse();

    await subject(req, res);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.send).toHaveBeenCalledWith();
  });

  it("logs the payload information", async () => {
    const request = mockRequest(payload);
    const response = mockResponse();

    await subject(request, response);

    expect(log.info).toHaveBeenCalledWith("pixCashInWebhook", payload);
  });

  it("publishes converted notification to PubSub topic", async () => {
    const req = mockRequest(payload);
    const res = mockResponse();

    await subject(req, res);

    expect(mockTopic).toHaveBeenCalledWith("payments-on-pix-cashin");
    expect(mockPublish).toHaveBeenCalledWith({
      data: Buffer.from(
        JSON.stringify({
          id: "6870fa0e12dfd51ebc24f842",
          bank_receipt_url: null,
          end2end_id: "E9f93a1eab7724878b4c8c326c7a9f342",
        }),
      ),
    });
  });

  it("does not publish unknown event to PubSub topic", async () => {
    const req = mockRequest({});
    const res = mockResponse();

    await subject(req, res);

    expect(mockTopic).not.toHaveBeenCalled();
    expect(mockPublish).not.toHaveBeenCalled();
  });

  it("does not publish without correlationID to PubSub topic", async () => {
    const req = mockRequest({ ...payload, event: "oh-blah-dih" });
    const res = mockResponse();

    await subject(req, res);

    expect(mockTopic).not.toHaveBeenCalled();
    expect(mockPublish).not.toHaveBeenCalled();
  });
});
