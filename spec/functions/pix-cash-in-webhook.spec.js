const log = require("../../app/lib/log");

log.info = jest.fn().mockResolvedValue();
log.error = jest.fn().mockResolvedValue();

const mockPublish = jest.fn().mockResolvedValue();
const mockTopic = jest.fn().mockReturnValue({
  publishMessage: mockPublish,
});

jest.mock("@google-cloud/pubsub", () => ({
  PubSub: jest.fn().mockImplementation(() => ({
    topic: mockTopic,
  })),
}));

const mockRequest = (body) => ({
  body,
});

const mockResponse = () => {
  const response = {};

  response.status = jest.fn().mockReturnValue(response);
  response.send = jest.fn().mockReturnValue(response);

  return response;
};

const subject = require("../../app/functions/pix-cash-in-webhook");

beforeEach(() => {
  log.info.mockClear();
  log.error.mockClear();

  mockPublish.mockClear();
  mockTopic.mockClear();
});

const payload = {
  event: "OPENPIX:TRANSACTION_RECEIVED",
  pix: {
    endToEndId: "E9f93a1eab7724878b4c8c326c7a9f342",
    transactionID: "6870fa0e12dfd51ebc24f842",
  },
};

describe("#pixCashInWebhook", () => {
  it("responds with status 200", async () => {
    const req = mockRequest(payload);
    const res = mockResponse();

    await subject(req, res);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.send).toHaveBeenCalledWith();
  });

  it("logs the payload information", async () => {
    const request = mockRequest(payload);
    const response = mockResponse();

    await subject(request, response);

    expect(log.info).toHaveBeenCalledWith("pixCashInWebhook", payload);
  });

  it("publishes converted notification to PubSub topic", async () => {
    const req = mockRequest(payload);
    const res = mockResponse();

    await subject(req, res);

    expect(mockTopic).toHaveBeenCalledWith("payments-on-pix-cashin");
    expect(mockPublish).toHaveBeenCalledWith({
      data: Buffer.from(
        JSON.stringify({
          id: "6870fa0e12dfd51ebc24f842",
          bank_receipt_url: null,
          end2end_id: "E9f93a1eab7724878b4c8c326c7a9f342",
        }),
      ),
    });
  });

  it("does not publish unknown event to PubSub topic", async () => {
    const req = mockRequest({});
    const res = mockResponse();

    await subject(req, res);

    expect(mockTopic).not.toHaveBeenCalled();
    expect(mockPublish).not.toHaveBeenCalled();
  });

  it("does not publish without transactionID to PubSub topic", async () => {
    const req = mockRequest({ ...payload, event: "oh-blah-dih" });
    const res = mockResponse();

    await subject(req, res);

    expect(mockTopic).not.toHaveBeenCalled();
    expect(mockPublish).not.toHaveBeenCalled();
  });

  describe("error handling", () => {
    it("logs error and responds with status 500 when PubSub publish fails", async () => {
      const publishError = new Error("PubSub connection failed");
      mockPublish.mockRejectedValueOnce(publishError);

      const req = mockRequest(payload);
      const res = mockResponse();

      await subject(req, res);

      expect(log.error).toHaveBeenCalledWith(publishError);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith();
    });

    it("logs error and responds with status 500 when convert function throws", async () => {
      const malformedPayload = {
        event: "OPENPIX:TRANSACTION_RECEIVED",
        pix: {
          transactionID: "6870fa0e12dfd51ebc24f842",
        },
      };

      const req = mockRequest(malformedPayload);
      const res = mockResponse();

      await subject(req, res);

      expect(log.error).toHaveBeenCalledWith(expect.any(Error));
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith();
    });

    it("logs error and responds with status 500 when log.info throws", async () => {
      const logError = new Error("Logging service unavailable");
      log.info.mockImplementationOnce(() => {
        throw logError;
      });

      const req = mockRequest(payload);
      const res = mockResponse();

      await subject(req, res);

      expect(log.error).toHaveBeenCalledWith(logError);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith();
    });

    it("logs error and responds with status 500 when topic method throws", async () => {
      const topicError = new Error("Topic not found");
      mockTopic.mockImplementationOnce(() => {
        throw topicError;
      });

      const req = mockRequest(payload);
      const res = mockResponse();

      await subject(req, res);

      expect(log.error).toHaveBeenCalledWith(topicError);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith();
    });

    it("logs error and responds with status 500 when Buffer.from throws", async () => {
      const originalBufferFrom = Buffer.from;
      const bufferError = new Error("Buffer allocation failed");

      Buffer.from = jest.fn().mockImplementation(() => {
        throw bufferError;
      });

      const req = mockRequest(payload);
      const res = mockResponse();

      await subject(req, res);

      expect(log.error).toHaveBeenCalledWith(bufferError);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith();

      Buffer.from = originalBufferFrom;
    });
  });
});
