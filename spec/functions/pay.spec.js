const axios = require("axios");
const AxiosMockAdapter = require("axios-mock-adapter");

const log = require("../../app/lib/log");

log.error = jest.fn().mockResolvedValue();
log.info = jest.fn().mockResolvedValue();

function base64(data) {
  return Buffer.from(JSON.stringify(data)).toString("base64");
}

// Mocks the Axios.
const axiosMockAdapter = new AxiosMockAdapter(axios);
const CREATE_PAYMENT_ENDPOINT = `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/payment`;
const APPROVE_PAYMENT_ENDPOINT = `${CREATE_PAYMENT_ENDPOINT}/approve`;
axiosMockAdapter.onPost(CREATE_PAYMENT_ENDPOINT).reply(200, { payment: { status: "CREATED" } });
axiosMockAdapter.onPost(APPROVE_PAYMENT_ENDPOINT).reply(200, { payment: { status: "APPROVED" } });

const transfer = {
  entry_id: "582f87f7985e540855000001",
  tasker_name: "John Doe",
  tasker_email: "<EMAIL>",
  tasker_cpf: "***********",
  bank_code: "341",
  bank_agency: "0445",
  bank_account: "3442311",
  bank_account_type: "checking",
  value: 8281,
};
const message = { data: base64(transfer) };

const subject = require("../../app/functions/pay");

beforeEach(() => {
  axiosMockAdapter.resetHistory();
  log.error.mockClear();
  log.info.mockClear();
});

describe("#pay", () => {
  it("returns true", async () => {
    const result = await subject(message);

    expect(result).toBe(true);
  });

  it("writes info to log", async () => {
    await subject(message);

    expect(log.info).toHaveBeenCalledWith("pay (entry_id: 582f87f7985e540855000001,c:true,a:true)", {
      transfer: {
        entry_id: "582f87f7985e540855000001",
        tasker_name: "John Doe",
        tasker_email: "<EMAIL>",
        tasker_cpf: "***********",
        bank_code: "341",
        bank_agency: "0445",
        bank_account: "********000003442311",
        bank_account_type: "checking",
        value: 8281,
      },
      created: true,
      approved: true,
    });
  });

  it("requests to create and approve payment on Woovi", async () => {
    await subject(message);

    expect(JSON.parse(axiosMockAdapter.history.post[0].data)).toMatchObject({
      value: 8281,
      correlationID: "582f87f7985e540855000001",
      holder: {
        name: "John Doe",
        taxID: {
          type: "BR:CPF",
          taxID: "***********",
        },
      },
      psp: {
        id: "********",
        name: "ITAU UNIBANCO S.A.",
      },
      account: {
        account: "********000003442311",
        branch: "0445",
        accountType: "CACC",
      },
    });

    expect(JSON.parse(axiosMockAdapter.history.post[1].data)).toMatchObject({
      correlationID: "582f87f7985e540855000001",
    });
  });

  it("applies rules about bank account for Banco do Brasil", async () => {
    await subject({
      data: base64({
        ...transfer,
        bank_code: "001",
        bank_agency: "1458",
        bank_account: "5060-X",
      }),
    });

    expect(JSON.parse(axiosMockAdapter.history.post[0].data)).toMatchObject({
      value: 8281,
      correlationID: "582f87f7985e540855000001",
      holder: {
        name: "John Doe",
        taxID: {
          type: "BR:CPF",
          taxID: "***********",
        },
      },
      psp: {
        id: "********",
        name: "BCO DO BRASIL S.A.",
      },
      account: {
        account: "********000000050600",
        branch: "1458",
        accountType: "CACC",
      },
    });

    expect(JSON.parse(axiosMockAdapter.history.post[1].data)).toMatchObject({
      correlationID: "582f87f7985e540855000001",
    });
  });

  describe("when fails on validation", () => {
    it("refuses when value is greather than R$2.500.00", async () => {
      const result = await subject({
        data: base64({
          ...transfer,
          value: 250001,
        }),
      });

      expect(log.error).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "pay: validation exception (entry_id: 582f87f7985e540855000001)",
          data: expect.objectContaining({
            transfer: expect.objectContaining({
              value: 250001,
            }),
            errors: expect.arrayContaining(["value must be less than or equal to 250000"]),
          }),
        }),
      );
      expect(result).toBe(true);
    });

    it("logs the errors, but returns true because the same payload cannot be processed again", async () => {
      const result = await subject({
        data: base64({
          entry_id: "582f87f7985e540855000001",
          tasker_name: "",
          tasker_email: "",
          tasker_cpf: "",
          bank_code: "",
          bank_agency: "",
          bank_account: "",
          bank_account_type: "",
          value: 0,
        }),
      });

      expect(log.error).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "pay: validation exception (entry_id: 582f87f7985e540855000001)",
          data: expect.objectContaining({
            transfer: {
              entry_id: "582f87f7985e540855000001",
              tasker_name: "",
              tasker_email: "",
              tasker_cpf: "",
              bank_code: "",
              bank_agency: "0000",
              bank_account: "****************0000",
              bank_account_type: "",
              value: 0,
            },
            errors: expect.any(Array),
          }),
        }),
      );
      expect(result).toBe(true);
    });
  });
});
