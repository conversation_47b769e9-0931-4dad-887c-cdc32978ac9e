const { PubSub } = require("@google-cloud/pubsub");

const log = require("../../app/lib/log");

// Mocks the pubsub.
jest.mock("@google-cloud/pubsub");
const mPublishMessage = jest.fn().mockResolvedValue("mock-message-id");
const mTopic = jest.fn().mockReturnValue({ publishMessage: mPublishMessage });
PubSub.mockImplementation(() => ({ topic: mTopic }));

log.error = jest.fn().mockResolvedValue();
log.info = jest.fn().mockResolvedValue();

function base64(data) {
  return Buffer.from(JSON.stringify(data)).toString("base64");
}

const transfer = {
  entry_id: "582f87f7985e540855000001",
  tasker_name: "<PERSON>",
  tasker_email: "<EMAIL>",
  tasker_cpf: "***********",
  bank_code: "341",
  bank_agency: "0445",
  bank_account: "0344231",
  bank_account_type: "checking",
  value: 8281,
};

const payload = { batch_name: null, transfers: [transfer] };
const message = { data: base64(payload) };

const subject = require("../../app/functions/process-payment-batch");

beforeEach(() => {
  log.error.mockClear();
  log.info.mockClear();
  mPublishMessage.mockClear();
  mTopic.mockClear();
});

describe("#processPaymentBatch", () => {
  it("returns true", async () => {
    const result = await subject(message);

    expect(result).toBe(true);
  });

  it("writes info to log", async () => {
    await subject(message);

    expect(log.info).toHaveBeenCalledWith("process-payment-batch", { length: 1 });
  });

  it("does not write error to log", async () => {
    await subject(message);

    expect(log.error).not.toHaveBeenCalled();
  });

  it("publishes each transfer to pubsub to pay", async () => {
    await subject(message);

    expect(mPublishMessage).toHaveBeenCalledWith({ data: Buffer.from(JSON.stringify(transfer)) });
  });

  describe("when receives a batch name", () => {
    it("sends the created batch information to pubsub", async () => {
      await subject({
        data: base64({
          ...payload,
          batch_name: "BATCH_NAME",
        }),
      });

      expect(mPublishMessage).toHaveBeenCalledTimes(2);
      expect(mPublishMessage).toHaveBeenNthCalledWith(2, {
        data: Buffer.from(
          JSON.stringify({
            batch_name: "BATCH_NAME",
            entry_ids: [transfer.entry_id],
          }),
        ),
      });
    });
  });

  describe("when does not receives a batch name", () => {
    it("sends the created batch information to pubsub with generated batch name", async () => {
      await subject({
        data: base64({
          ...payload,
          batch_name: null,
        }),
      });

      expect(mPublishMessage).toHaveBeenCalledTimes(2);
      const batchCallArgs = mPublishMessage.mock.calls[1][0];
      const publishedData = JSON.parse(batchCallArgs.data.toString());
      expect(publishedData.batch_name).toMatch(/^batch-/);
      expect(publishedData.entry_ids).toEqual([transfer.entry_id]);
    });
  });
});
